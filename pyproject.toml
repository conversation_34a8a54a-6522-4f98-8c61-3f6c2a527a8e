[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "photo-center"
version = "0.1.0"
description = "Intelligent photo centering using computer vision"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Photo Center Team"},
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: End Users/Desktop",
    "Intended Audience :: Developers",
    "Topic :: Multimedia :: Graphics :: Graphics Conversion",
    "Topic :: Scientific/Engineering :: Image Processing",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
]
requires-python = ">=3.8"
dependencies = [
    # Core dependencies
    "opencv-python>=4.8.0",
    "numpy>=1.24.0",
    "Pillow>=10.0.0",
    "rawpy>=0.18.0",
    "imageio>=2.31.0",
    
    # Computer Vision and ML
    "ultralytics>=8.0.0",
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    
    # UI Framework
    "PySide6>=6.5.0",
    
    # Configuration and utilities
    "PyYAML>=6.0",
    "click>=8.1.0",
    
    # Image processing extras
    "scikit-image>=0.21.0",
    "matplotlib>=3.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.0.0",
]
gpu = [
    "torch>=2.0.0+cu118",
    "torchvision>=0.15.0+cu118",
]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
]

[project.scripts]
photo-center = "photo_center.main:main"
photo-center-gui = "photo_center.ui.main_window:main"
photo-center-test = "photo_center.test_installation:main"

[project.urls]
Homepage = "https://github.com/your-username/photo-center"
Repository = "https://github.com/your-username/photo-center"
Issues = "https://github.com/your-username/photo-center/issues"

[tool.hatch.build.targets.wheel]
packages = ["src/photo_center"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/tests",
    "/config.yaml",
    "/README.md",
]

# Black configuration
[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# Pytest configuration
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "gpu: marks tests that require GPU",
]

# MyPy configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "cv2.*",
    "rawpy.*",
    "ultralytics.*",
    "PySide6.*",
]
ignore_missing_imports = true

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
