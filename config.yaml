# Photo Center Configuration

# Model settings
models:
  human_detection:
    model_path: "yolov8n-pose.pt"  # Will be downloaded automatically
    confidence_threshold: 0.5
    device: "cpu"  # or "cuda" if GPU available
  
# Image processing settings
image_processing:
  output_format: "jpg"  # jpg, png, tiff
  output_quality: 95
  preserve_raw: true
  max_output_size: [3000, 3000]  # [width, height] or null for original size
  
# Centering algorithm settings
centering:
  method: "keypoint_based"  # keypoint_based, bbox_based, center_of_mass
  target_position: [0.5, 0.4]  # [x, y] as fraction of image (0.5, 0.4 = slightly above center)
  margin_ratio: 0.1  # Minimum margin around subject as fraction of image
  crop_aspect_ratio: null  # null for original ratio, or [16, 9] for specific ratio
  
# Reference matching settings
reference_matching:
  enabled: false
  similarity_threshold: 0.8
  keypoint_weights:
    nose: 1.0
    eyes: 0.8
    shoulders: 0.6
    
# Batch processing settings
batch:
  supported_extensions: [".cr2", ".nef", ".arw", ".dng", ".raw", ".jpg", ".jpeg", ".png", ".tiff"]
  output_suffix: "_centered"
  create_subdirectory: true
  subdirectory_name: "centered"
  max_workers: 4  # Number of parallel processes
  
# UI settings
ui:
  preview_size: [800, 600]
  show_keypoints: true
  show_bounding_box: true
  auto_refresh: true
  
# Logging settings
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "photo_center.log"
  max_size_mb: 10
  backup_count: 3
