#!/usr/bin/env python3
"""
Development helper script for Photo Center.
"""

import subprocess
import sys
import argparse
from pathlib import Path


def run_command(cmd, description=None):
    """Run a command and handle errors."""
    if description:
        print(f"\n{description}...")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Command failed with exit code {e.returncode}")
        return False


def test(args):
    """Run tests."""
    cmd = "uv run pytest"
    
    if args.coverage:
        cmd += " --cov=src/photo_center --cov-report=html --cov-report=term"
    
    if args.verbose:
        cmd += " -v"
    
    if args.pattern:
        cmd += f" -k {args.pattern}"
    
    return run_command(cmd, "Running tests")


def lint(args):
    """Run linting tools."""
    commands = []
    
    if args.black or args.all:
        commands.append(("uv run black src/ tests/ scripts/", "Running Black formatter"))
    
    if args.flake8 or args.all:
        commands.append(("uv run flake8 src/ tests/", "Running Flake8 linter"))
    
    if args.mypy or args.all:
        commands.append(("uv run mypy src/", "Running MyPy type checker"))
    
    if not commands:
        commands = [
            ("uv run black src/ tests/ scripts/", "Running Black formatter"),
            ("uv run flake8 src/ tests/", "Running Flake8 linter"),
        ]
    
    success = True
    for cmd, desc in commands:
        if not run_command(cmd, desc):
            success = False
    
    return success


def install(args):
    """Install dependencies."""
    cmd = "uv sync"
    
    if args.dev:
        cmd += " --extra dev"
    
    if args.gpu:
        cmd += " --extra gpu"
    
    return run_command(cmd, "Installing dependencies")


def clean(args):
    """Clean build artifacts."""
    commands = [
        "find . -type d -name __pycache__ -exec rm -rf {} +",
        "find . -type f -name '*.pyc' -delete",
        "rm -rf build/ dist/ *.egg-info/",
        "rm -rf .pytest_cache/ .coverage htmlcov/",
        "rm -rf .mypy_cache/",
    ]
    
    for cmd in commands:
        run_command(cmd)
    
    print("✓ Cleaned build artifacts")
    return True


def run_app(args):
    """Run the application."""
    cmd = "uv run photo-center"
    
    if args.cli:
        cmd += " --cli"
    
    if args.input:
        cmd += f" -i {args.input}"
    
    if args.output:
        cmd += f" -o {args.output}"
    
    if args.batch:
        cmd += " --batch"
    
    if args.recursive:
        cmd += " --recursive"
    
    return run_command(cmd, "Running Photo Center")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Photo Center Development Helper")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Test command
    test_parser = subparsers.add_parser("test", help="Run tests")
    test_parser.add_argument("--coverage", action="store_true", help="Run with coverage")
    test_parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    test_parser.add_argument("--pattern", "-k", help="Test pattern to match")
    
    # Lint command
    lint_parser = subparsers.add_parser("lint", help="Run linting tools")
    lint_parser.add_argument("--black", action="store_true", help="Run Black only")
    lint_parser.add_argument("--flake8", action="store_true", help="Run Flake8 only")
    lint_parser.add_argument("--mypy", action="store_true", help="Run MyPy only")
    lint_parser.add_argument("--all", action="store_true", help="Run all linting tools")
    
    # Install command
    install_parser = subparsers.add_parser("install", help="Install dependencies")
    install_parser.add_argument("--dev", action="store_true", help="Install dev dependencies")
    install_parser.add_argument("--gpu", action="store_true", help="Install GPU dependencies")
    
    # Clean command
    subparsers.add_parser("clean", help="Clean build artifacts")
    
    # Run command
    run_parser = subparsers.add_parser("run", help="Run the application")
    run_parser.add_argument("--cli", action="store_true", help="Force CLI mode")
    run_parser.add_argument("-i", "--input", help="Input file or directory")
    run_parser.add_argument("-o", "--output", help="Output file or directory")
    run_parser.add_argument("--batch", action="store_true", help="Batch mode")
    run_parser.add_argument("--recursive", action="store_true", help="Recursive processing")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # Map commands to functions
    commands = {
        "test": test,
        "lint": lint,
        "install": install,
        "clean": clean,
        "run": run_app,
    }
    
    if args.command in commands:
        success = commands[args.command](args)
        return 0 if success else 1
    else:
        print(f"Unknown command: {args.command}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
