#!/usr/bin/env python3
"""
Setup script for Photo Center with UV.
"""

import subprocess
import sys
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n{description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed")
        print(f"Error: {e.stderr}")
        return False


def main():
    """Main setup function."""
    print("Photo Center Setup with UV")
    print("=" * 30)
    
    # Check if UV is installed
    try:
        subprocess.run(["uv", "--version"], check=True, capture_output=True)
        print("✓ UV is installed")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ UV is not installed")
        print("Please install UV first: https://docs.astral.sh/uv/getting-started/installation/")
        return 1
    
    # Change to project root
    project_root = Path(__file__).parent.parent
    print(f"Working in: {project_root}")
    
    # Setup commands
    commands = [
        ("uv sync", "Installing dependencies"),
        ("uv run photo-center-test", "Running installation tests"),
    ]
    
    success = True
    for cmd, desc in commands:
        if not run_command(cmd, desc):
            success = False
            break
    
    if success:
        print("\n" + "=" * 30)
        print("✓ Setup completed successfully!")
        print("\nNext steps:")
        print("  - Run 'uv run photo-center' to start the GUI")
        print("  - Run 'uv run photo-center --help' for CLI options")
        print("  - Run 'uv run pytest' to run tests")
    else:
        print("\n" + "=" * 30)
        print("✗ Setup failed")
        print("Please check the error messages above")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
