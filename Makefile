# Photo Center Makefile for UV commands
# Usage: make <target>

.PHONY: help install install-dev install-gpu test test-cov lint format clean run run-cli setup

# Default target
help:
	@echo "Photo Center - Available commands:"
	@echo ""
	@echo "Setup and Installation:"
	@echo "  setup       - Initial setup with UV"
	@echo "  install     - Install dependencies"
	@echo "  install-dev - Install with dev dependencies"
	@echo "  install-gpu - Install with GPU support"
	@echo ""
	@echo "Development:"
	@echo "  test        - Run tests"
	@echo "  test-cov    - Run tests with coverage"
	@echo "  lint        - Run linting tools"
	@echo "  format      - Format code with Black"
	@echo "  clean       - Clean build artifacts"
	@echo ""
	@echo "Running:"
	@echo "  run         - Launch GUI"
	@echo "  run-cli     - Show CLI help"
	@echo "  test-install - Run installation test"

# Setup and Installation
setup:
	@echo "Setting up Photo Center with UV..."
	uv sync
	uv run photo-center-test

install:
	uv sync

install-dev:
	uv sync --extra dev

install-gpu:
	uv sync --extra gpu

# Testing
test:
	uv run pytest

test-cov:
	uv run pytest --cov=src/photo_center --cov-report=html --cov-report=term

test-install:
	uv run photo-center-test

# Development
lint:
	uv run python scripts/dev.py lint --all

format:
	uv run black src/ tests/ scripts/

clean:
	uv run python scripts/dev.py clean

# Running
run:
	uv run photo-center

run-cli:
	uv run photo-center --help

# Lock file management
lock:
	uv lock

# Build
build:
	uv build

# Show UV environment info
info:
	uv info
