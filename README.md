# Photo Center - Intelligent Photo Centering

Photo Center is a Python application that automatically centers human subjects in photos using computer vision and machine learning. It supports both RAW and standard image formats, provides both GUI and CLI interfaces, and can process images individually or in batches.

## Features

- **Human Detection**: Uses YOLO models for accurate human detection and pose estimation
- **Intelligent Centering**: Multiple centering algorithms including keypoint-based, bounding box, and center of mass
- **RAW Image Support**: Full support for RAW image formats (CR2, NEF, ARW, DNG, etc.) using rawpy
- **Batch Processing**: Process entire directories of images with parallel processing support
- **GUI Interface**: User-friendly interface built with PySide6/Qt
- **CLI Interface**: Command-line interface for automation and scripting
- **Configurable**: Extensive configuration options for fine-tuning behavior
- **Reference Matching**: Optional feature to match poses with reference images
- **Comprehensive Testing**: Full test suite for reliable operation

## Installation

### Prerequisites

- Python 3.8 or higher
- [UV](https://docs.astral.sh/uv/) (recommended) or pip
- CUDA-capable GPU (optional, for faster processing)

### Quick Setup with UV (Recommended)

```bash
# Install UV if you haven't already
curl -LsSf https://astral.sh/uv/install.sh | sh

# Clone the repository
git clone <repository-url>
cd photocenter

# Install dependencies and setup environment
uv sync

# Run installation test
uv run photo-center-test
```

### Alternative Setup with pip

```bash
# Clone the repository
git clone <repository-url>
cd photocenter

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### GPU Support (Optional)

For faster processing with CUDA:

```bash
# With UV
uv sync --extra gpu

# With pip
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

## Quick Start

### GUI Mode

Launch the graphical interface:

```bash
# With UV (recommended)
uv run photo-center

# With pip/python
python main.py
```

### CLI Mode

Process a single image:

```bash
# With UV
uv run photo-center -i input_image.jpg -o centered_image.jpg

# With pip/python
python main.py -i input_image.jpg -o centered_image.jpg
```

Batch process a directory:

```bash
# With UV
uv run photo-center -i /path/to/images -o /path/to/output --batch

# With pip/python
python main.py -i /path/to/images -o /path/to/output --batch
```

Batch process with recursion:

```bash
# With UV
uv run photo-center -i /path/to/images --batch --recursive

# With pip/python
python main.py -i /path/to/images --batch --recursive
```

## Configuration

The application uses a YAML configuration file (`config.yaml`) for settings:

```yaml
# Model settings
models:
  human_detection:
    model_path: "yolov8n-pose.pt"
    confidence_threshold: 0.5
    device: "cpu"  # or "cuda"

# Centering settings
centering:
  method: "keypoint_based"  # keypoint_based, bbox_based, center_of_mass
  target_position: [0.5, 0.4]  # [x, y] as fraction of image
  margin_ratio: 0.1

# Image processing
image_processing:
  output_format: "jpg"
  output_quality: 95
  preserve_raw: true

# Batch processing
batch:
  max_workers: 4
  output_suffix: "_centered"
```

## Usage Examples

### GUI Interface

1. **Single Image Processing**:
   - Click "Load Image" to select an image
   - Click "Process Image" to center the subject
   - Use preview controls to view original, processed, and visualization
   - Click "Save Result" to save the centered image

2. **Batch Processing**:
   - Click "Select Input Directory" to choose source folder
   - Optionally select output directory (or use default subdirectory)
   - Enable "Process subdirectories recursively" if needed
   - Click "Start Batch Processing"

3. **Settings**:
   - Adjust confidence threshold for detection sensitivity
   - Set target position for subject placement
   - Choose centering method
   - Select output format

### CLI Interface

```bash
# Process single image with custom settings
uv run photo-center -i photo.cr2 -o centered.jpg --cli

# Batch process RAW files
uv run photo-center -i /photos/raw --batch --recursive

# Use custom configuration
uv run photo-center -i image.jpg --config custom_config.yaml
```

## Centering Methods

### 1. Keypoint-Based (Recommended)
Uses human pose estimation to identify key body points (nose, eyes, shoulders, hips) and calculates an optimal center point with weighted importance (head > shoulders > hips).

### 2. Bounding Box
Uses the center of the detected human bounding box as the subject center.

### 3. Center of Mass
Calculates the center of mass within the detected human region for more precise centering of the actual subject matter.

## Supported File Formats

### Input Formats
- **RAW**: CR2, NEF, ARW, DNG, RAW, ORF, RW2, PEF, SRW
- **Standard**: JPG, JPEG, PNG, TIFF

### Output Formats
- JPEG (with quality control)
- PNG (lossless)
- TIFF (high quality)

## Architecture

```
src/photo_center/
├── models/                 # AI models for detection
│   └── human_detector.py   # YOLO-based human detection
├── image_processing/       # Image processing modules
│   ├── raw_processor.py    # RAW image handling
│   └── centering.py        # Core centering algorithms
├── batch/                  # Batch processing
│   └── batch_processor.py  # Directory processing
├── ui/                     # User interface
│   ├── main_window.py      # Main GUI window
│   └── preview_widget.py   # Image preview component
└── utils/                  # Utilities
    ├── config.py           # Configuration management
    └── logger.py           # Logging setup
```

## Testing

Run the test suite:

```bash
# With UV (recommended)
# Run installation test
uv run photo-center-test

# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=src/photo_center --cov-report=html

# Run specific test file
uv run pytest tests/test_centering.py

# With pip/python
pytest
pytest --cov=src/photo_center
```

## Development

### Setting up Development Environment

```bash
# With UV (recommended)
# Install all dependencies including dev tools
uv sync --extra dev

# Use the development helper script
uv run python scripts/dev.py --help

# Run code formatting
uv run python scripts/dev.py lint --black

# Run all linting tools
uv run python scripts/dev.py lint --all

# Run tests with coverage
uv run python scripts/dev.py test --coverage

# With pip/python
pip install -r requirements.txt
pip install -e ".[dev]"
black src/ tests/
flake8 src/ tests/
mypy src/
```

### UV Commands Reference

```bash
# Environment management
uv sync                    # Install dependencies
uv sync --extra dev        # Install with dev dependencies
uv sync --extra gpu        # Install with GPU support
uv lock                    # Update lock file

# Running the application
uv run photo-center        # Launch GUI
uv run photo-center --help # Show CLI help
uv run photo-center-test   # Run installation test

# Development tools
uv run pytest             # Run tests
uv run black src/          # Format code
uv run flake8 src/         # Lint code
uv run mypy src/           # Type checking

# Helper scripts
uv run python scripts/dev.py test --coverage
uv run python scripts/dev.py lint --all
uv run python scripts/dev.py clean
```

### Adding New Features

1. **New Centering Method**: Add to `centering.py` and update configuration
2. **New Image Format**: Extend `raw_processor.py` 
3. **New UI Component**: Add to `ui/` directory
4. **New Model**: Add to `models/` directory

## Performance Tips

1. **GPU Acceleration**: Set `device: "cuda"` in config for faster processing
2. **Parallel Processing**: Increase `max_workers` for batch operations
3. **Image Size**: Use `max_output_size` to limit output resolution
4. **Model Selection**: Use larger YOLO models (yolov8s, yolov8m) for better accuracy

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce batch size or use CPU processing
2. **RAW File Not Supported**: Install latest rawpy version
3. **GUI Not Starting**: Install PySide6 or use CLI mode
4. **No Human Detected**: Lower confidence threshold or check image quality

### Logging

Enable debug logging in `config.yaml`:

```yaml
logging:
  level: "DEBUG"
  file: "photo_center.log"
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

[Add your license information here]

## Acknowledgments

- [Ultralytics YOLO](https://github.com/ultralytics/ultralytics) for human detection
- [rawpy](https://github.com/letmaik/rawpy) for RAW image processing
- [OpenCV](https://opencv.org/) for computer vision operations
- [PySide6](https://doc.qt.io/qtforpython/) for GUI framework
