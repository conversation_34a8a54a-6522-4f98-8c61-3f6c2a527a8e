#!/usr/bin/env python3
"""
Photo Center - Intelligent Photo Centering Application

Main entry point for the Photo Center application.
Provides both CLI and GUI interfaces for photo centering operations.
"""

import sys
import argparse
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.photo_center.utils.config import Config
from src.photo_center.utils.logger import setup_logger


def run_gui():
    """Run the GUI application."""
    from src.photo_center.ui.main_window import main
    main()


def run_cli(args):
    """Run CLI operations."""
    from src.photo_center.models.human_detector import HumanDetector
    from src.photo_center.image_processing.raw_processor import RawProcessor
    from src.photo_center.image_processing.centering import PhotoCenterer
    from src.photo_center.batch.batch_processor import BatchProcessor
    
    # Setup logging
    config = Config()
    logger = setup_logger(
        level=config.log_level,
        log_file=config.log_file
    )
    
    if args.batch:
        # Batch processing
        logger.info(f"Starting batch processing: {args.input}")
        
        batch_processor = BatchProcessor(config)
        
        def progress_callback(current, total):
            print(f"Progress: {current}/{total} ({current/total*100:.1f}%)")
        
        result = batch_processor.process_directory(
            args.input,
            args.output,
            args.recursive,
            progress_callback
        )
        
        print(f"\nBatch processing completed:")
        print(f"  Total files: {result.total_files}")
        print(f"  Processed: {result.processed_files}")
        print(f"  Failed: {result.failed_files}")
        print(f"  Skipped: {result.skipped_files}")
        print(f"  Processing time: {result.processing_time:.2f} seconds")
        
    else:
        # Single file processing
        if not args.input:
            print("Error: Input file required for single file processing")
            return 1
        
        logger.info(f"Processing single file: {args.input}")
        
        # Initialize components
        raw_processor = RawProcessor(config)
        human_detector = HumanDetector(config)
        photo_centerer = PhotoCenterer(config)
        
        try:
            # Load image
            image = raw_processor.load_image(args.input)
            print(f"Loaded image: {args.input}")
            
            # Detect humans
            detections = human_detector.detect_humans(image)
            
            if not detections:
                print("No human detected in image")
                return 1
            
            print(f"Detected {len(detections)} human(s)")
            
            # Get best detection
            best_detection = human_detector.get_best_detection(detections)
            print(f"Best detection confidence: {best_detection['confidence']:.3f}")
            
            # Center subject
            centering_result = photo_centerer.center_subject(image, best_detection)
            print(f"Centering confidence: {centering_result.confidence:.3f}")
            print(f"Method used: {centering_result.method_used}")
            
            # Save result
            output_path = args.output or f"{Path(args.input).stem}_centered.jpg"
            raw_processor.save_image(centering_result.cropped_image, output_path)
            print(f"Result saved to: {output_path}")
            
        except Exception as e:
            logger.error(f"Error processing file: {e}")
            print(f"Error: {e}")
            return 1
    
    return 0


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Photo Center - Intelligent Photo Centering",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Launch GUI
  python main.py

  # Process single image
  python main.py -i image.jpg -o centered.jpg

  # Batch process directory
  python main.py -i /path/to/images -o /path/to/output --batch

  # Batch process with recursion
  python main.py -i /path/to/images --batch --recursive
        """
    )
    
    parser.add_argument(
        "-i", "--input",
        help="Input image file or directory"
    )
    
    parser.add_argument(
        "-o", "--output",
        help="Output file or directory"
    )
    
    parser.add_argument(
        "--batch",
        action="store_true",
        help="Enable batch processing mode"
    )
    
    parser.add_argument(
        "--recursive",
        action="store_true",
        help="Process directories recursively (batch mode only)"
    )
    
    parser.add_argument(
        "--cli",
        action="store_true",
        help="Force CLI mode (no GUI)"
    )
    
    parser.add_argument(
        "--config",
        help="Path to configuration file"
    )
    
    args = parser.parse_args()
    
    # Determine mode
    if args.cli or args.input:
        # CLI mode
        return run_cli(args)
    else:
        # GUI mode
        try:
            run_gui()
            return 0
        except ImportError as e:
            print(f"GUI dependencies not available: {e}")
            print("Please install PySide6 or use CLI mode with --cli flag")
            return 1
        except Exception as e:
            print(f"Error running GUI: {e}")
            return 1


if __name__ == "__main__":
    sys.exit(main())
