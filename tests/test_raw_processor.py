"""Tests for RAW image processing."""

import pytest
import numpy as np
import cv2
import tempfile
from pathlib import Path

import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.photo_center.image_processing.raw_processor import RawProcessor
from src.photo_center.utils.config import Config


class TestRawProcessor:
    """Test RAW image processing functionality."""
    
    @pytest.fixture
    def processor(self):
        """Create RawProcessor instance for testing."""
        return RawProcessor()
    
    @pytest.fixture
    def test_image(self):
        """Create a test image."""
        # Create a simple test image (RGB)
        image = np.zeros((100, 100, 3), dtype=np.uint8)
        image[25:75, 25:75] = [255, 0, 0]  # Red square in center
        return cv2.cvtColor(image, cv2.COLOR_RGB2BGR)  # Convert to BGR for OpenCV
    
    def test_is_raw_file(self, processor):
        """Test RAW file detection."""
        # Test RAW extensions
        assert processor.is_raw_file("image.cr2")
        assert processor.is_raw_file("image.nef")
        assert processor.is_raw_file("image.arw")
        assert processor.is_raw_file("image.dng")
        assert processor.is_raw_file("image.raw")
        
        # Test case insensitive
        assert processor.is_raw_file("image.CR2")
        assert processor.is_raw_file("image.NEF")
        
        # Test non-RAW extensions
        assert not processor.is_raw_file("image.jpg")
        assert not processor.is_raw_file("image.png")
        assert not processor.is_raw_file("image.tiff")
    
    def test_save_and_load_standard_image(self, processor, test_image):
        """Test saving and loading standard image formats."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Test JPEG
            jpg_path = temp_path / "test.jpg"
            processor.save_image(test_image, jpg_path)
            assert jpg_path.exists()
            
            loaded_image = processor.load_image(jpg_path)
            assert loaded_image.shape == test_image.shape
            assert loaded_image.dtype == test_image.dtype
            
            # Test PNG
            png_path = temp_path / "test.png"
            processor.save_image(test_image, png_path, format_override='png')
            assert png_path.exists()
            
            loaded_png = processor.load_image(png_path)
            assert loaded_png.shape == test_image.shape
    
    def test_resize_image(self, processor, test_image):
        """Test image resizing functionality."""
        # Test resize with max size
        resized = processor.resize_image(test_image, max_size=(50, 50))
        assert resized.shape[0] <= 50
        assert resized.shape[1] <= 50
        
        # Test no resize when image is smaller
        small_resized = processor.resize_image(test_image, max_size=(200, 200))
        assert small_resized.shape == test_image.shape
        
        # Test resize without maintaining aspect ratio
        stretched = processor.resize_image(
            test_image, 
            max_size=(200, 50), 
            maintain_aspect=False
        )
        assert stretched.shape[:2] == (50, 200)
    
    def test_get_image_info(self, processor, test_image):
        """Test getting image information."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir) / "test.jpg"
            processor.save_image(test_image, temp_path)
            
            info = processor.get_image_info(temp_path)
            
            assert info['path'] == str(temp_path)
            assert info['exists'] is True
            assert info['size_bytes'] > 0
            assert info['is_raw'] is False
            assert info['extension'] == '.jpg'
            assert 'width' in info
            assert 'height' in info
    
    def test_load_nonexistent_file(self, processor):
        """Test loading non-existent file."""
        with pytest.raises(FileNotFoundError):
            processor.load_image("/nonexistent/file.jpg")
    
    def test_save_image_quality(self, processor, test_image):
        """Test saving with different quality settings."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Save with high quality
            high_quality_path = temp_path / "high_quality.jpg"
            processor.save_image(test_image, high_quality_path, quality=95)
            
            # Save with low quality
            low_quality_path = temp_path / "low_quality.jpg"
            processor.save_image(test_image, low_quality_path, quality=50)
            
            # High quality should result in larger file
            high_size = high_quality_path.stat().st_size
            low_size = low_quality_path.stat().st_size
            assert high_size > low_size
