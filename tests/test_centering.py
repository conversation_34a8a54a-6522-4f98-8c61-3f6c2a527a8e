"""Tests for photo centering functionality."""

import pytest
import numpy as np
import cv2
from unittest.mock import Mock

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.photo_center.image_processing.centering import Photo<PERSON>enterer, CenteringResult
from src.photo_center.utils.config import Config


class TestPhotoCenterer:
    """Test photo centering functionality."""
    
    @pytest.fixture
    def centerer(self):
        """Create PhotoCenterer instance for testing."""
        return PhotoCenterer()
    
    @pytest.fixture
    def test_image(self):
        """Create a test image."""
        # Create a 400x300 test image
        image = np.zeros((300, 400, 3), dtype=np.uint8)
        # Add some content
        cv2.rectangle(image, (150, 100), (250, 200), (255, 255, 255), -1)
        return image
    
    @pytest.fixture
    def mock_detection_bbox(self):
        """Create a mock detection with bounding box."""
        return {
            'bbox': [150, 100, 250, 200],  # x1, y1, x2, y2
            'confidence': 0.85,
            'center': (200, 150),
            'keypoints': None
        }
    
    @pytest.fixture
    def mock_detection_keypoints(self):
        """Create a mock detection with keypoints."""
        return {
            'bbox': [150, 100, 250, 200],
            'confidence': 0.90,
            'center': (200, 150),
            'keypoints': {
                'nose': (200, 120),
                'left_eye': (190, 115),
                'right_eye': (210, 115),
                'left_shoulder': (180, 140),
                'right_shoulder': (220, 140),
                'left_hip': (185, 180),
                'right_hip': (215, 180)
            }
        }
    
    def test_center_by_bbox(self, centerer, test_image, mock_detection_bbox):
        """Test centering using bounding box method."""
        result = centerer._center_by_bbox(test_image, mock_detection_bbox)
        
        assert isinstance(result, CenteringResult)
        assert result.method_used == 'bbox_based'
        assert result.confidence >= 0.0
        assert result.confidence <= 1.0
        assert result.cropped_image.shape[2] == 3  # RGB channels
        assert len(result.crop_box) == 4  # x1, y1, x2, y2
        assert len(result.subject_center) == 2  # x, y
        assert len(result.target_center) == 2  # x, y
    
    def test_center_by_keypoints(self, centerer, test_image, mock_detection_keypoints):
        """Test centering using keypoints method."""
        result = centerer._center_by_keypoints(test_image, mock_detection_keypoints)
        
        assert isinstance(result, CenteringResult)
        assert result.method_used == 'keypoint_based'
        assert result.confidence >= 0.0
        assert result.confidence <= 1.0
        assert result.cropped_image.shape[2] == 3
    
    def test_center_by_mass(self, centerer, test_image, mock_detection_bbox):
        """Test centering using center of mass method."""
        result = centerer._center_by_mass(test_image, mock_detection_bbox)
        
        assert isinstance(result, CenteringResult)
        assert result.method_used == 'center_of_mass'
        assert result.confidence >= 0.0
        assert result.confidence <= 1.0
    
    def test_center_subject_auto_method(self, centerer, test_image, mock_detection_keypoints):
        """Test automatic method selection in center_subject."""
        # Should use keypoint method when keypoints available
        result = centerer.center_subject(test_image, mock_detection_keypoints)
        assert result.method_used == 'keypoint_based'
        
        # Should fallback to bbox when no keypoints
        detection_no_keypoints = mock_detection_keypoints.copy()
        detection_no_keypoints['keypoints'] = None
        
        result = centerer.center_subject(test_image, detection_no_keypoints)
        assert result.method_used == 'bbox_based'
    
    def test_center_subject_with_target_size(self, centerer, test_image, mock_detection_bbox):
        """Test centering with specific target size."""
        target_size = (200, 200)
        result = centerer.center_subject(test_image, mock_detection_bbox, target_size)
        
        assert result.cropped_image.shape[:2] == target_size[::-1]  # height, width
    
    def test_visualize_centering(self, centerer, test_image, mock_detection_bbox):
        """Test centering visualization."""
        result = centerer.center_subject(test_image, mock_detection_bbox)
        vis_image = centerer.visualize_centering(result)
        
        assert vis_image.shape == result.cropped_image.shape
        assert vis_image.dtype == result.cropped_image.dtype
        # Visualization should be different from original (has overlays)
        assert not np.array_equal(vis_image, result.cropped_image)
    
    def test_crop_box_bounds(self, centerer, test_image, mock_detection_bbox):
        """Test that crop box stays within image bounds."""
        result = centerer.center_subject(test_image, mock_detection_bbox)
        
        x1, y1, x2, y2 = result.crop_box
        height, width = test_image.shape[:2]
        
        assert x1 >= 0
        assert y1 >= 0
        assert x2 <= width
        assert y2 <= height
        assert x2 > x1
        assert y2 > y1
    
    def test_keypoint_weighting(self, centerer, test_image):
        """Test that head keypoints get higher weight than body keypoints."""
        # Create detection with head and body keypoints
        detection = {
            'bbox': [100, 50, 300, 250],
            'confidence': 0.9,
            'center': (200, 150),
            'keypoints': {
                'nose': (200, 80),  # Head area
                'left_hip': (180, 200),  # Body area
                'right_hip': (220, 200)
            }
        }
        
        result = centerer._center_by_keypoints(test_image, detection)
        
        # The calculated center should be closer to head than to hips
        # This is a basic test - in practice, the weighting should pull
        # the center towards the head area
        assert isinstance(result, CenteringResult)
        assert result.method_used == 'keypoint_based'
    
    def test_empty_keypoints_fallback(self, centerer, test_image):
        """Test fallback to bbox method when keypoints are empty."""
        detection = {
            'bbox': [150, 100, 250, 200],
            'confidence': 0.85,
            'center': (200, 150),
            'keypoints': {}  # Empty keypoints
        }
        
        result = centerer._center_by_keypoints(test_image, detection)
        # Should fallback to bbox method
        assert result.method_used == 'bbox_based'
