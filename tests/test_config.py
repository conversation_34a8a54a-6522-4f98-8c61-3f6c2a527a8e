"""Tests for configuration management."""

import pytest
import tempfile
import yaml
from pathlib import Path

import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.photo_center.utils.config import Config


class TestConfig:
    """Test configuration management."""
    
    def test_config_loading(self):
        """Test loading configuration from file."""
        # Create temporary config file
        config_data = {
            'models': {
                'human_detection': {
                    'confidence_threshold': 0.7,
                    'device': 'cpu'
                }
            },
            'centering': {
                'method': 'keypoint_based',
                'target_position': [0.5, 0.4]
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            config = Config(config_path)
            
            # Test getting values
            assert config.get('models.human_detection.confidence_threshold') == 0.7
            assert config.get('models.human_detection.device') == 'cpu'
            assert config.get('centering.method') == 'keypoint_based'
            assert config.get('centering.target_position') == [0.5, 0.4]
            
            # Test default values
            assert config.get('nonexistent.key', 'default') == 'default'
            
        finally:
            Path(config_path).unlink()
    
    def test_config_properties(self):
        """Test configuration convenience properties."""
        config_data = {
            'models': {
                'human_detection': {
                    'confidence_threshold': 0.8,
                    'device': 'cuda'
                }
            },
            'image_processing': {
                'output_format': 'png',
                'output_quality': 90
            },
            'centering': {
                'target_position': [0.6, 0.3],
                'margin_ratio': 0.15
            },
            'batch': {
                'supported_extensions': ['.jpg', '.png'],
                'max_workers': 8
            },
            'logging': {
                'level': 'DEBUG',
                'file': 'test.log'
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            config = Config(config_path)
            
            # Test properties
            assert config.model_confidence_threshold == 0.8
            assert config.model_device == 'cuda'
            assert config.output_format == 'png'
            assert config.output_quality == 90
            assert config.target_position == (0.6, 0.3)
            assert config.margin_ratio == 0.15
            assert config.supported_extensions == ['.jpg', '.png']
            assert config.max_workers == 8
            assert config.log_level == 'DEBUG'
            assert config.log_file == 'test.log'
            
        finally:
            Path(config_path).unlink()
    
    def test_config_set_and_save(self):
        """Test setting and saving configuration values."""
        config_data = {
            'models': {
                'human_detection': {
                    'confidence_threshold': 0.5
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            config = Config(config_path)
            
            # Test setting values
            config.set('models.human_detection.confidence_threshold', 0.9)
            config.set('new.nested.key', 'value')
            
            assert config.get('models.human_detection.confidence_threshold') == 0.9
            assert config.get('new.nested.key') == 'value'
            
            # Test saving
            config.save()
            
            # Load again and verify
            config2 = Config(config_path)
            assert config2.get('models.human_detection.confidence_threshold') == 0.9
            assert config2.get('new.nested.key') == 'value'
            
        finally:
            Path(config_path).unlink()
    
    def test_config_file_not_found(self):
        """Test handling of missing configuration file."""
        with pytest.raises(FileNotFoundError):
            Config('/nonexistent/config.yaml')
