# Photo Center UV Guide

This guide covers how to use UV (the fast Python package manager) with Photo Center for development and testing.

## Why UV?

UV is a fast Python package manager that provides:
- **Speed**: 10-100x faster than pip
- **Reliability**: Deterministic dependency resolution
- **Simplicity**: Single tool for package management and virtual environments
- **Compatibility**: Works with existing Python projects

## Quick Start

### 1. Install UV

```bash
# On macOS and Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# On Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# Or with pip
pip install uv
```

### 2. Setup Photo Center

```bash
# Clone the repository
git clone <repository-url>
cd photocenter

# Install dependencies and setup environment
uv sync

# Test the installation
uv run photo-center-test
```

### 3. Run the Application

```bash
# Launch GUI
uv run photo-center

# CLI help
uv run photo-center --help

# Process a single image
uv run photo-center -i image.jpg -o centered.jpg

# Batch process
uv run photo-center -i /photos --batch --recursive
```

## UV Commands for Photo Center

### Environment Management

```bash
# Install all dependencies
uv sync

# Install with development tools
uv sync --extra dev

# Install with GPU support
uv sync --extra gpu

# Update dependencies
uv lock
uv sync
```

### Running Applications

```bash
# Available scripts (defined in pyproject.toml)
uv run photo-center          # Main application
uv run photo-center-gui      # GUI only
uv run photo-center-test     # Installation test

# Run Python modules directly
uv run python -m photo_center.main
uv run python main.py
```

### Development and Testing

```bash
# Run tests
uv run pytest
uv run pytest --cov=src/photo_center
uv run pytest tests/test_centering.py -v

# Code quality
uv run black src/ tests/
uv run flake8 src/ tests/
uv run mypy src/

# Development helper script
uv run python scripts/dev.py test --coverage
uv run python scripts/dev.py lint --all
uv run python scripts/dev.py clean
```

### Using Make Commands

For convenience, use the provided Makefile:

```bash
# Setup
make setup

# Development
make test
make test-cov
make lint
make format

# Running
make run
make run-cli
```

## Project Structure for UV

The project is configured for UV with these key files:

```
photocenter/
├── pyproject.toml          # Project configuration and dependencies
├── uv.lock                 # Lock file (auto-generated)
├── Makefile                # Convenience commands
├── scripts/
│   ├── setup.py           # Setup helper
│   └── dev.py             # Development helper
└── src/photo_center/
    ├── main.py            # UV-compatible entry point
    └── test_installation.py # UV-compatible test script
```

## Configuration Details

### pyproject.toml

The `pyproject.toml` file defines:
- **Dependencies**: Core packages needed to run
- **Optional dependencies**: Dev tools, GPU support
- **Scripts**: Entry points for `uv run`
- **Tool configuration**: Black, pytest, mypy settings

### Scripts

Defined in `pyproject.toml`:
- `photo-center`: Main application entry point
- `photo-center-gui`: GUI-only entry point
- `photo-center-test`: Installation test

### Optional Dependencies

```bash
# Development tools
uv sync --extra dev
# Includes: pytest, black, flake8, mypy, etc.

# GPU support
uv sync --extra gpu
# Includes: CUDA-enabled PyTorch

# Both
uv sync --extra dev --extra gpu
```

## Troubleshooting

### Common Issues

1. **UV not found**
   ```bash
   # Make sure UV is in your PATH
   echo $PATH
   # Restart your shell after installation
   ```

2. **Dependencies not found**
   ```bash
   # Sync dependencies
   uv sync
   # Check lock file
   uv lock --check
   ```

3. **Import errors**
   ```bash
   # Run installation test
   uv run photo-center-test
   # Check Python path
   uv run python -c "import sys; print(sys.path)"
   ```

### Development Workflow

1. **Make changes** to the code
2. **Run tests**: `uv run pytest` or `make test`
3. **Format code**: `uv run black src/` or `make format`
4. **Check linting**: `uv run flake8 src/` or `make lint`
5. **Test application**: `uv run photo-center-test`

### Performance Tips

- Use `uv sync` instead of `pip install` for faster dependency resolution
- Use `uv run` instead of activating virtual environments
- Use `uv lock` to update dependencies
- Use `--extra` flags to install only needed dependencies

## Migration from pip

If you're coming from pip:

```bash
# Old way
pip install -r requirements.txt
python main.py

# New way with UV
uv sync
uv run photo-center
```

## Advanced Usage

### Custom Python Version

```bash
# Use specific Python version
uv sync --python 3.11
```

### Environment Variables

```bash
# Set environment variables for UV
export UV_CACHE_DIR=/custom/cache
export UV_INDEX_URL=https://custom.pypi.org/simple
```

### Building and Distribution

```bash
# Build wheel
uv build

# Install from wheel
uv pip install dist/photo_center-0.1.0-py3-none-any.whl
```

## Resources

- [UV Documentation](https://docs.astral.sh/uv/)
- [UV GitHub Repository](https://github.com/astral-sh/uv)
- [Python Packaging Guide](https://packaging.python.org/)

## Summary

UV provides a modern, fast way to manage Python dependencies and environments. For Photo Center:

1. **Install UV** and run `uv sync`
2. **Use `uv run photo-center`** to launch the application
3. **Use `uv run pytest`** for testing
4. **Use the helper scripts** in `scripts/` for development tasks
5. **Use the Makefile** for common commands

This setup ensures reproducible builds, fast dependency resolution, and a smooth development experience.
