"""Human detection using YOLO models."""

import cv2
import numpy as np
from typing import List, Tu<PERSON>, Dict, Optional, Any
from ultralytics import <PERSON>OL<PERSON>
from pathlib import Path

from ..utils.logger import get_logger
from ..utils.config import Config


class HumanDetector:
    """Human detection and pose estimation using YOLO."""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize human detector.
        
        Args:
            config: Configuration object. If None, creates default config.
        """
        self.config = config or Config()
        self.logger = get_logger(__name__)
        self.model = None
        self._load_model()
    
    def _load_model(self) -> None:
        """Load YOLO model for human detection and pose estimation."""
        try:
            model_path = self.config.get('models.human_detection.model_path', 'yolov8n-pose.pt')
            device = self.config.model_device
            
            self.logger.info(f"Loading YOLO model: {model_path} on device: {device}")
            self.model = YOLO(model_path)
            
            # Move model to specified device
            if device == 'cuda':
                self.model.to('cuda')
            
            self.logger.info("YOLO model loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load YOLO model: {e}")
            raise
    
    def detect_humans(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Detect humans in image and return detection results.
        
        Args:
            image: Input image as numpy array (BGR format)
            
        Returns:
            List of detection dictionaries containing:
            - bbox: [x1, y1, x2, y2] bounding box coordinates
            - confidence: Detection confidence score
            - keypoints: List of keypoint coordinates (if available)
            - center: Center point of the detection
        """
        if self.model is None:
            raise RuntimeError("Model not loaded")
        
        try:
            # Run inference
            results = self.model(image, verbose=False)
            
            detections = []
            confidence_threshold = self.config.model_confidence_threshold
            
            for result in results:
                # Process bounding boxes
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()
                    classes = result.boxes.cls.cpu().numpy()
                    
                    # Process keypoints if available
                    keypoints = None
                    if hasattr(result, 'keypoints') and result.keypoints is not None:
                        keypoints = result.keypoints.xy.cpu().numpy()
                    
                    for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
                        # Filter by confidence and class (person = 0 in COCO)
                        if conf >= confidence_threshold and cls == 0:
                            x1, y1, x2, y2 = box
                            center_x = (x1 + x2) / 2
                            center_y = (y1 + y2) / 2
                            
                            detection = {
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'confidence': float(conf),
                                'center': (int(center_x), int(center_y)),
                                'keypoints': None
                            }
                            
                            # Add keypoints if available
                            if keypoints is not None and i < len(keypoints):
                                kpts = keypoints[i]
                                if len(kpts) > 0:
                                    detection['keypoints'] = self._process_keypoints(kpts)
                            
                            detections.append(detection)
            
            self.logger.debug(f"Detected {len(detections)} humans with confidence >= {confidence_threshold}")
            return detections
            
        except Exception as e:
            self.logger.error(f"Error during human detection: {e}")
            raise
    
    def _process_keypoints(self, keypoints: np.ndarray) -> Dict[str, Tuple[int, int]]:
        """Process raw keypoints into named dictionary.
        
        Args:
            keypoints: Raw keypoints array from YOLO
            
        Returns:
            Dictionary mapping keypoint names to (x, y) coordinates
        """
        # COCO keypoint names (17 keypoints)
        keypoint_names = [
            'nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear',
            'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
            'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
            'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
        ]
        
        processed_keypoints = {}
        
        for i, name in enumerate(keypoint_names):
            if i < len(keypoints):
                x, y = keypoints[i]
                # Only include keypoints with valid coordinates
                if x > 0 and y > 0:
                    processed_keypoints[name] = (int(x), int(y))
        
        return processed_keypoints
    
    def get_best_detection(self, detections: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Get the best human detection from a list of detections.
        
        Args:
            detections: List of detection dictionaries
            
        Returns:
            Best detection based on confidence and size, or None if no detections
        """
        if not detections:
            return None
        
        # Sort by confidence first, then by bounding box area
        def detection_score(det):
            bbox = det['bbox']
            area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
            return det['confidence'] * 0.7 + (area / 1000000) * 0.3  # Weighted score
        
        best_detection = max(detections, key=detection_score)
        self.logger.debug(f"Selected best detection with confidence: {best_detection['confidence']:.3f}")
        
        return best_detection
    
    def visualize_detections(self, image: np.ndarray, detections: List[Dict[str, Any]]) -> np.ndarray:
        """Visualize detections on image.
        
        Args:
            image: Input image
            detections: List of detection dictionaries
            
        Returns:
            Image with visualized detections
        """
        vis_image = image.copy()
        
        for detection in detections:
            bbox = detection['bbox']
            confidence = detection['confidence']
            keypoints = detection.get('keypoints', {})
            
            # Draw bounding box
            cv2.rectangle(vis_image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
            
            # Draw confidence
            label = f"Person: {confidence:.2f}"
            cv2.putText(vis_image, label, (bbox[0], bbox[1] - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # Draw keypoints
            if keypoints:
                for name, (x, y) in keypoints.items():
                    cv2.circle(vis_image, (x, y), 3, (0, 0, 255), -1)
                    cv2.putText(vis_image, name, (x + 5, y), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
        
        return vis_image
