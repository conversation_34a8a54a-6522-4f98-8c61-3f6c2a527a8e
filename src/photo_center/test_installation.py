#!/usr/bin/env python3
"""
Test script to verify Photo Center installation and basic functionality.
UV-compatible version.
"""

import sys
import traceback
import numpy as np
import cv2


def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        # Core dependencies
        import cv2  # noqa: F401
        import numpy as np  # noqa: F401
        import rawpy  # noqa: F401
        import yaml  # noqa: F401
        print("✓ Core dependencies imported successfully")
        
        # Photo Center modules
        from photo_center.utils.config import Config  # noqa: F401
        from photo_center.utils.logger import setup_logger  # noqa: F401
        from photo_center.models.human_detector import HumanDetector  # noqa: F401
        from photo_center.image_processing.raw_processor import RawProcessor  # noqa: F401
        from photo_center.image_processing.centering import PhotoCenterer  # noqa: F401
        from photo_center.batch.batch_processor import BatchProcessor  # noqa: F401
        print("✓ Photo Center modules imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error during import: {e}")
        traceback.print_exc()
        return False


def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        from photo_center.utils.config import Config
        
        config = Config()
        
        # Test basic properties
        confidence = config.model_confidence_threshold
        device = config.model_device
        output_format = config.output_format
        
        print("✓ Configuration loaded successfully")
        print(f"  - Confidence threshold: {confidence}")
        print(f"  - Device: {device}")
        print(f"  - Output format: {output_format}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        traceback.print_exc()
        return False


def test_image_processing():
    """Test basic image processing functionality."""
    print("\nTesting image processing...")
    
    try:
        from photo_center.image_processing.raw_processor import RawProcessor
        from photo_center.image_processing.centering import PhotoCenterer
        
        # Create test image
        test_image = np.zeros((300, 400, 3), dtype=np.uint8)
        test_image[100:200, 150:250] = [255, 255, 255]  # White rectangle
        
        # Test raw processor
        processor = RawProcessor()
        
        # Test resize
        resized = processor.resize_image(test_image, max_size=(200, 200))
        print(f"✓ Image resizing works: {test_image.shape} -> {resized.shape}")
        
        # Test centering (with mock detection)
        centerer = PhotoCenterer()
        mock_detection = {
            'bbox': [150, 100, 250, 200],
            'confidence': 0.85,
            'center': (200, 150),
            'keypoints': None
        }
        
        result = centerer.center_subject(test_image, mock_detection)
        print(f"✓ Photo centering works: confidence={result.confidence:.3f}, method={result.method_used}")
        
        return True
        
    except Exception as e:
        print(f"✗ Image processing error: {e}")
        traceback.print_exc()
        return False


def test_model_loading():
    """Test YOLO model loading (may download model)."""
    print("\nTesting model loading...")
    
    try:
        from photo_center.models.human_detector import HumanDetector
        
        print("  Loading YOLO model (this may take a moment)...")
        detector = HumanDetector()
        
        # Create test image with a simple shape
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        # Add some content that might be detected
        cv2.rectangle(test_image, (200, 100), (400, 400), (128, 128, 128), -1)
        cv2.circle(test_image, (300, 200), 50, (255, 255, 255), -1)
        
        # Test detection (may not detect anything in synthetic image)
        detections = detector.detect_humans(test_image)
        print(f"✓ Model loaded and inference works (detected {len(detections)} humans)")
        
        return True
        
    except Exception as e:
        print(f"✗ Model loading error: {e}")
        print("  Note: This might be due to network issues downloading the model")
        traceback.print_exc()
        return False


def test_gui_dependencies():
    """Test GUI dependencies (optional)."""
    print("\nTesting GUI dependencies...")
    
    try:
        import PySide6  # noqa: F401
        from PySide6.QtWidgets import QApplication  # noqa: F401
        print("✓ PySide6 available - GUI mode supported")
        return True
        
    except ImportError:
        print("⚠ PySide6 not available - GUI mode not supported (CLI only)")
        return True  # Not a failure, just a limitation


def main():
    """Run all tests."""
    print("Photo Center Installation Test (UV Version)")
    print("=" * 45)
    
    tests = [
        test_imports,
        test_configuration,
        test_image_processing,
        test_gui_dependencies,
        test_model_loading,  # This one last as it may take time
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except KeyboardInterrupt:
            print("\n\nTest interrupted by user")
            break
        except Exception as e:
            print(f"\n✗ Unexpected error in {test.__name__}: {e}")
            results.append(False)
    
    print("\n" + "=" * 45)
    print("Test Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ All {total} tests passed!")
        print("\nPhoto Center is ready to use!")
        print("Run 'uv run photo-center' to start the GUI")
        print("Run 'uv run photo-center --help' for CLI options")
        return 0
    else:
        print(f"✗ {total - passed} out of {total} tests failed")
        print("\nSome functionality may not work correctly.")
        print("Please check the error messages above and install missing dependencies.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
