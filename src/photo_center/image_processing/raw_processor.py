"""RAW image processing using rawpy."""

import rawpy
import imageio
import numpy as np
from pathlib import Path
from typing import Op<PERSON>, Tuple, Union
from PIL import Image
import cv2

from ..utils.logger import get_logger
from ..utils.config import Config


class RawProcessor:
    """RAW image processor using rawpy and imageio."""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize RAW processor.
        
        Args:
            config: Configuration object. If None, creates default config.
        """
        self.config = config or Config()
        self.logger = get_logger(__name__)
        
        # Supported RAW extensions
        self.raw_extensions = {'.cr2', '.nef', '.arw', '.dng', '.raw', '.orf', '.rw2', '.pef', '.srw'}
        
    def is_raw_file(self, file_path: Union[str, Path]) -> bool:
        """Check if file is a RAW image file.
        
        Args:
            file_path: Path to image file
            
        Returns:
            True if file is a RAW image
        """
        return Path(file_path).suffix.lower() in self.raw_extensions
    
    def load_image(self, file_path: Union[str, Path]) -> np.ndarray:
        """Load image from file (RAW or standard format).
        
        Args:
            file_path: Path to image file
            
        Returns:
            Image as numpy array in BGR format (for OpenCV compatibility)
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Image file not found: {file_path}")
        
        try:
            if self.is_raw_file(file_path):
                return self._load_raw_image(file_path)
            else:
                return self._load_standard_image(file_path)
                
        except Exception as e:
            self.logger.error(f"Failed to load image {file_path}: {e}")
            raise
    
    def _load_raw_image(self, file_path: Path) -> np.ndarray:
        """Load RAW image using rawpy.
        
        Args:
            file_path: Path to RAW image file
            
        Returns:
            Processed image as numpy array in BGR format
        """
        self.logger.info(f"Loading RAW image: {file_path}")
        
        with rawpy.imread(str(file_path)) as raw:
            # Post-process RAW image
            # Use default settings for now, can be customized later
            rgb_image = raw.postprocess(
                use_camera_wb=True,  # Use camera white balance
                half_size=False,     # Full resolution
                no_auto_bright=True, # Disable auto brightness
                output_bps=16        # 16-bit output
            )
        
        # Convert from RGB to BGR for OpenCV
        bgr_image = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR)
        
        # Convert from 16-bit to 8-bit if needed
        if bgr_image.dtype == np.uint16:
            bgr_image = (bgr_image / 256).astype(np.uint8)
        
        self.logger.debug(f"RAW image loaded: shape={bgr_image.shape}, dtype={bgr_image.dtype}")
        return bgr_image
    
    def _load_standard_image(self, file_path: Path) -> np.ndarray:
        """Load standard image format using OpenCV.
        
        Args:
            file_path: Path to image file
            
        Returns:
            Image as numpy array in BGR format
        """
        self.logger.debug(f"Loading standard image: {file_path}")
        
        image = cv2.imread(str(file_path))
        if image is None:
            raise ValueError(f"Could not load image: {file_path}")
        
        return image
    
    def save_image(
        self, 
        image: np.ndarray, 
        output_path: Union[str, Path],
        quality: Optional[int] = None,
        format_override: Optional[str] = None
    ) -> None:
        """Save image to file.
        
        Args:
            image: Image as numpy array in BGR format
            output_path: Output file path
            quality: JPEG quality (1-100). If None, uses config default
            format_override: Override output format. If None, uses config default
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Determine output format
        output_format = format_override or self.config.output_format
        quality = quality or self.config.output_quality
        
        try:
            if output_format.lower() in ['jpg', 'jpeg']:
                # Save as JPEG
                cv2.imwrite(
                    str(output_path.with_suffix('.jpg')),
                    image,
                    [cv2.IMWRITE_JPEG_QUALITY, quality]
                )
            elif output_format.lower() == 'png':
                # Save as PNG
                cv2.imwrite(str(output_path.with_suffix('.png')), image)
            elif output_format.lower() in ['tiff', 'tif']:
                # Save as TIFF
                cv2.imwrite(str(output_path.with_suffix('.tiff')), image)
            else:
                # Default to JPEG
                cv2.imwrite(
                    str(output_path.with_suffix('.jpg')),
                    image,
                    [cv2.IMWRITE_JPEG_QUALITY, quality]
                )
            
            self.logger.info(f"Image saved: {output_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save image {output_path}: {e}")
            raise
    
    def resize_image(
        self, 
        image: np.ndarray, 
        max_size: Optional[Tuple[int, int]] = None,
        maintain_aspect: bool = True
    ) -> np.ndarray:
        """Resize image while maintaining aspect ratio.
        
        Args:
            image: Input image
            max_size: Maximum size as (width, height). If None, uses config default
            maintain_aspect: Whether to maintain aspect ratio
            
        Returns:
            Resized image
        """
        if max_size is None:
            max_size = self.config.get('image_processing.max_output_size')
            if max_size is None:
                return image  # No resizing
        
        height, width = image.shape[:2]
        max_width, max_height = max_size
        
        if width <= max_width and height <= max_height:
            return image  # No resizing needed
        
        if maintain_aspect:
            # Calculate scaling factor
            scale = min(max_width / width, max_height / height)
            new_width = int(width * scale)
            new_height = int(height * scale)
        else:
            new_width, new_height = max_width, max_height
        
        resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        self.logger.debug(f"Image resized from {width}x{height} to {new_width}x{new_height}")
        
        return resized
    
    def get_image_info(self, file_path: Union[str, Path]) -> dict:
        """Get image information without loading the full image.
        
        Args:
            file_path: Path to image file
            
        Returns:
            Dictionary with image information
        """
        file_path = Path(file_path)
        
        info = {
            'path': str(file_path),
            'exists': file_path.exists(),
            'size_bytes': file_path.stat().st_size if file_path.exists() else 0,
            'is_raw': self.is_raw_file(file_path),
            'extension': file_path.suffix.lower()
        }
        
        try:
            if self.is_raw_file(file_path):
                # Get RAW image info
                with rawpy.imread(str(file_path)) as raw:
                    info.update({
                        'width': raw.sizes.width,
                        'height': raw.sizes.height,
                        'camera_make': getattr(raw, 'camera_make', 'Unknown'),
                        'camera_model': getattr(raw, 'camera_model', 'Unknown'),
                    })
            else:
                # Get standard image info using PIL
                with Image.open(file_path) as img:
                    info.update({
                        'width': img.width,
                        'height': img.height,
                        'mode': img.mode,
                        'format': img.format
                    })
        
        except Exception as e:
            self.logger.warning(f"Could not get image info for {file_path}: {e}")
            info['error'] = str(e)
        
        return info
